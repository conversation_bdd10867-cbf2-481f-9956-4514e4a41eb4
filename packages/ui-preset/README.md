# SAF UI Preset

A self-managed design system with CSS variables and Tailwind CSS configuration, inspired by shadcn/ui patterns.

## Overview

This package provides a comprehensive design system built with:
- **CSS Custom Properties** - All design tokens defined as CSS variables
- **Tailwind CSS Integration** - Seamless integration with Tailwind CSS
- **Dark Mode Support** - Built-in dark mode with class-based switching
- **Self-Managed** - No external dependencies or design token tooling required

## Features

- 🎨 **Complete Design System** - Colors, typography, spacing, shadows, and more
- 🌙 **Dark Mode Ready** - Automatic dark mode support with CSS variables
- 🔧 **Tailwind Integration** - Works seamlessly with Tailwind CSS
- 📦 **Zero Dependencies** - No external design token tools or Figma integration
- 🎯 **shadcn/ui Compatible** - Follows shadcn/ui patterns and conventions

## Installation

```bash
npm install @saf/ui-preset
# or
yarn add @saf/ui-preset
# or
pnpm add @saf/ui-preset
```

## Usage

### 1. Configure Tailwind CSS

Add the preset to your `tailwind.config.js`:

```js
import { preset } from '@saf/ui-preset'

/** @type {import('tailwindcss').Config} */
export default {
  presets: [preset],
  content: [
    // your content paths
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  // your other config
}
```

### 2. Import CSS Variables

Import the base styles in your main CSS file:

```css
@import '@saf/ui-preset/dist/styles/base.css';
@import '@saf/ui-preset/dist/styles/shadows.css';
```

Or import them in your JavaScript/TypeScript entry point:

```js
import '@saf/ui-preset/dist/styles/base.css'
import '@saf/ui-preset/dist/styles/shadows.css'
```

### 3. Add Dark Mode Support

Add the `dark` class to your HTML element to enable dark mode:

```html
<html class="dark">
  <!-- your app -->
</html>
```

Or toggle it dynamically:

```js
// Enable dark mode
document.documentElement.classList.add('dark')

// Disable dark mode
document.documentElement.classList.remove('dark')
```

## Available Colors

The preset provides both shadcn/ui compatible colors and extended UI-specific colors:

### Core Colors (shadcn/ui compatible)
- `background` / `foreground`
- `card` / `card-foreground`
- `popover` / `popover-foreground`
- `primary` / `primary-foreground`
- `secondary` / `secondary-foreground`
- `muted` / `muted-foreground`
- `accent` / `accent-foreground`
- `destructive` / `destructive-foreground`
- `border` / `input` / `ring`

### Extended UI Colors
- `ui-bg-*` - Background colors with hover/pressed states
- `ui-fg-*` - Foreground colors for text and icons
- `ui-border-*` - Border colors for different states
- `ui-button-*` - Button-specific colors
- `ui-tag-*` - Tag/badge colors in multiple variants

## Typography

The preset includes a comprehensive typography system with classes like:
- `.h1-webs`, `.h2-webs`, `.h3-webs` - Heading styles
- `.txt-compact-*`, `.txt-*` - Text styles in various sizes
- `.code-*` - Code and monospace text styles

## Shadows

Pre-defined shadow utilities for common UI elements:
- `elevation-*` - Card, modal, tooltip elevations
- `buttons-*` - Button shadow states
- `borders-*` - Border and focus shadows

## Customization

You can override any CSS variable by defining it in your own CSS:

```css
:root {
  --primary: 220 100% 50%; /* Custom primary color */
  --radius: 0.75rem; /* Custom border radius */
}

.dark {
  --primary: 220 100% 60%; /* Custom primary color for dark mode */
}
```

## Examples

### Using Core Colors

```jsx
// shadcn/ui compatible usage
<div className="bg-background text-foreground border border-border">
  <h1 className="text-primary">Primary heading</h1>
  <p className="text-muted-foreground">Muted text</p>
  <button className="bg-primary text-primary-foreground">
    Primary button
  </button>
</div>
```

### Using Extended UI Colors

```jsx
// Extended UI color system
<div className="bg-ui-bg-base border border-ui-border-base">
  <div className="bg-ui-bg-component hover:bg-ui-bg-component-hover">
    <span className="text-ui-fg-base">Base text</span>
    <span className="text-ui-fg-subtle">Subtle text</span>
  </div>

  <div className="flex gap-2">
    <span className="bg-ui-tag-blue-bg text-ui-tag-blue-text border border-ui-tag-blue-border px-2 py-1 rounded">
      Blue tag
    </span>
    <span className="bg-ui-tag-green-bg text-ui-tag-green-text border border-ui-tag-green-border px-2 py-1 rounded">
      Green tag
    </span>
  </div>
</div>
```

### Using Typography Classes

```jsx
<div>
  <h1 className="h1-webs">Large heading</h1>
  <h2 className="h2-webs">Medium heading</h2>
  <p className="txt-compact-medium">Body text</p>
  <code className="code-body">Code snippet</code>
</div>
```

## Migration from Figma-based Systems

If you're migrating from a Figma-based design token system:

1. **Remove Figma dependencies** - No more `@medusajs/toolbox` or Figma tokens
2. **Update imports** - Import CSS files instead of generated tokens
3. **Use CSS variables** - All tokens are now CSS custom properties
4. **Update class names** - Use the new Tailwind color classes

## Development

To build the preset:

```bash
npm run build
```

## License

MIT
```
