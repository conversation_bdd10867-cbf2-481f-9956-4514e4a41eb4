import { defineConfig } from "tsup";
import path from "path";

export default defineConfig({
  entry: ["src/index.ts"],
  format: ["cjs", "esm"],
  tsconfig: path.resolve(__dirname, "tsconfig.json"),
  dts: true,
  clean: true,
  publicDir: "src/styles",
  onSuccess: async () => {
    // Copy CSS files to dist/styles
    const fs = await import("fs/promises");
    const srcStylesDir = path.resolve(__dirname, "src/styles");
    const distStylesDir = path.resolve(__dirname, "dist/styles");

    try {
      await fs.mkdir(distStylesDir, { recursive: true });
      const files = await fs.readdir(srcStylesDir);

      for (const file of files) {
        if (file.endsWith(".css")) {
          await fs.copyFile(
            path.join(srcStylesDir, file),
            path.join(distStylesDir, file),
          );
        }
      }
      console.log("✅ CSS files copied to dist/styles");
    } catch (error) {
      console.error("❌ Failed to copy CSS files:", error);
    }
  },
});
