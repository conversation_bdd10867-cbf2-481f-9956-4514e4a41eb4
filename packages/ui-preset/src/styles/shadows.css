@layer base {
  :root {
    /* Shadow Effects - Light Mode */
    --shadow-borders-interactive-with-active: 0px 0px 0px 1px rgb(59 130 246), 0px 0px 0px 4px rgb(59 130 246 / 0.2);
    --shadow-buttons-danger-focus: 0px 0.75px 0px 0px rgb(255 255 255 / 0.2) inset, 0px 1px 2px 0px rgb(190 18 60 / 0.4), 0px 0px 0px 1px rgb(190 18 60), 0px 0px 0px 2px rgb(255 255 255), 0px 0px 0px 4px rgb(59 130 246 / 0.6);
    --shadow-details-contrast-on-bg-interactive: 0px 1px 2px 0px rgb(30 58 138 / 0.6);
    --shadow-borders-interactive-with-focus: 0px 1px 2px 0px rgb(30 58 138 / 0.5), 0px 0px 0px 1px rgb(59 130 246), 0px 0px 0px 2px rgb(255 255 255), 0px 0px 0px 4px rgb(59 130 246 / 0.6);
    --shadow-borders-error: 0px 0px 0px 1px rgb(225 29 72), 0px 0px 0px 3px rgb(225 29 72 / 0.15);
    --shadow-borders-focus: 0px 0px 0px 1px rgb(255 255 255), 0px 0px 0px 3px rgb(59 130 246 / 0.6);
    --shadow-borders-interactive-with-shadow: 0px 1px 2px 0px rgb(30 58 138 / 0.5), 0px 0px 0px 1px rgb(59 130 246);
    --shadow-buttons-danger: 0px 0.75px 0px 0px rgb(255 255 255 / 0.2) inset, 0px 1px 2px 0px rgb(190 18 60 / 0.4), 0px 0px 0px 1px rgb(190 18 60);
    --shadow-buttons-inverted-focus: 0px 0.75px 0px 0px rgb(255 255 255 / 0.2) inset, 0px 1px 2px 0px rgb(0 0 0 / 0.4), 0px 0px 0px 1px rgb(24 24 27), 0px 0px 0px 2px rgb(255 255 255), 0px 0px 0px 4px rgb(59 130 246 / 0.6);
    --shadow-elevation-card-hover: 0px 0px 0px 1px rgb(0 0 0 / 0.08), 0px 1px 2px -1px rgb(0 0 0 / 0.08), 0px 2px 8px 0px rgb(0 0 0 / 0.1);
    --shadow-details-switch-handle: 0px 0px 2px 1px rgb(255 255 255) inset, 0px 1px 0px 0px rgb(255 255 255) inset, 0px 0px 0px 0.5px rgb(0 0 0 / 0.02), 0px 5px 4px 0px rgb(0 0 0 / 0.02), 0px 3px 3px 0px rgb(0 0 0 / 0.04), 0px 1px 2px 0px rgb(0 0 0 / 0.12), 0px 0px 1px 0px rgb(0 0 0 / 0.08);
    --shadow-buttons-neutral: 0px 1px 2px 0px rgb(0 0 0 / 0.12), 0px 0px 0px 1px rgb(0 0 0 / 0.08);
    --shadow-borders-base: 0px 1px 2px 0px rgb(0 0 0 / 0.12), 0px 0px 0px 1px rgb(0 0 0 / 0.08);
    --shadow-elevation-card-rest: 0px 0px 0px 1px rgb(0 0 0 / 0.08), 0px 1px 2px -1px rgb(0 0 0 / 0.08), 0px 2px 4px 0px rgb(0 0 0 / 0.04);
    --shadow-buttons-neutral-focus: 0px 1px 2px 0px rgb(0 0 0 / 0.12), 0px 0px 0px 1px rgb(0 0 0 / 0.08), 0px 0px 0px 2px rgb(255 255 255), 0px 0px 0px 4px rgb(59 130 246 / 0.6);
    --shadow-details-switch-background-focus: 0px 0px 0px 1px rgb(255 255 255), 0px 0px 0px 3px rgb(59 130 246 / 0.6), 0px 1px 1px 0px rgb(0 0 0 / 0.04) inset, 0px 2px 4px 0px rgb(0 0 0 / 0.04) inset, 0px 0px 0px 0.75px rgb(0 0 0 / 0.06) inset, 0px 0px 8px 0px rgb(0 0 0 / 0.02) inset, 0px 2px 4px 0px rgb(0 0 0 / 0.04);
    --shadow-details-switch-background: 0px 1px 1px 0px rgb(0 0 0 / 0.04) inset, 0px 2px 4px 0px rgb(0 0 0 / 0.04) inset, 0px 0px 0px 0.75px rgb(0 0 0 / 0.06) inset, 0px 0px 8px 0px rgb(0 0 0 / 0.02) inset, 0px 2px 4px 0px rgb(0 0 0 / 0.04);
    --shadow-elevation-flyout: 0px 0px 0px 1px rgb(0 0 0 / 0.08), 0px 4px 8px 0px rgb(0 0 0 / 0.08), 0px 8px 16px 0px rgb(0 0 0 / 0.08);
    --shadow-elevation-tooltip: 0px 0px 0px 1px rgb(0 0 0 / 0.08), 0px 2px 4px 0px rgb(0 0 0 / 0.08), 0px 4px 8px 0px rgb(0 0 0 / 0.08);
    --shadow-elevation-modal: 0px 0px 0px 1px rgb(255 255 255) inset, 0px 0px 0px 1.5px rgb(228 228 231 / 0.6) inset, 0px 0px 0px 1px rgb(0 0 0 / 0.08), 0px 8px 16px 0px rgb(0 0 0 / 0.08), 0px 16px 32px 0px rgb(0 0 0 / 0.08);
    --shadow-elevation-code-block: 0px 0px 0px 1px rgb(24 24 27) inset, 0px 0px 0px 1.5px rgb(255 255 255 / 0.2) inset;
    --shadow-buttons-inverted: 0px 0.75px 0px 0px rgb(255 255 255 / 0.2) inset, 0px 1px 2px 0px rgb(0 0 0 / 0.4), 0px 0px 0px 1px rgb(24 24 27);
    --shadow-elevation-commandbar: 0px 0px 0px 0.75px rgb(39 39 42) inset, 0px 0px 0px 1.25px rgb(255 255 255 / 0.3) inset, 0px 8px 16px 0px rgb(0 0 0 / 0.08), 0px 16px 32px 0px rgb(0 0 0 / 0.08);
  }

  .dark {
    /* Shadow Effects - Dark Mode */
    --shadow-borders-interactive-with-shadow: 0px 1px 2px 0px rgb(219 234 254 / 0.5), 0px 0px 0px 1px rgb(96 165 250);
    --shadow-details-contrast-on-bg-interactive: 0px 1px 2px 0px rgb(30 58 138 / 0.6);
    --shadow-details-switch-handle: 0px 0px 2px 1px rgb(255 255 255) inset, 0px 1px 0px 0px rgb(255 255 255) inset, 0px 0px 0px 0.5px rgb(0 0 0 / 0.16), 0px 5px 4px 0px rgb(0 0 0 / 0.1), 0px 3px 3px 0px rgb(0 0 0 / 0.1), 0px 1px 2px 0px rgb(0 0 0 / 0.1), 0px 0px 1px 0px rgb(0 0 0 / 0.1);
    --shadow-borders-interactive-with-active: 0px 0px 0px 1px rgb(96 165 250), 0px 0px 0px 4px rgb(59 130 246 / 0.25);
    --shadow-borders-focus: 0px 0px 0px 1px rgb(24 24 27), 0px 0px 0px 3px rgb(96 165 250 / 0.8);
    --shadow-borders-interactive-with-focus: 0px 1px 2px 0px rgb(219 234 254 / 0.5), 0px 0px 0px 1px rgb(96 165 250), 0px 0px 0px 2px rgb(24 24 27), 0px 0px 0px 4px rgb(96 165 250 / 0.8);
    --shadow-details-switch-background-focus: 0px 0px 0px 1px rgb(24 24 27), 0px 0px 0px 3px rgb(96 165 250 / 0.8), 0px 1px 1px 0px rgb(0 0 0 / 0.1) inset, 0px 2px 4px 0px rgb(0 0 0 / 0.1) inset, 0px 0px 0px 0.75px rgb(255 255 255 / 0.12) inset, 0px 0px 8px 0px rgb(0 0 0 / 0.1) inset;
    --shadow-buttons-danger: 0px -1px 0px 0px rgb(255 255 255 / 0.16), 0px 0px 0px 1px rgb(255 255 255 / 0.12), 0px 0px 0px 1px rgb(159 18 57), 0px 0px 1px 1.5px rgb(0 0 0 / 0.24), 0px 2px 2px 0px rgb(0 0 0 / 0.24);
    --shadow-buttons-danger-focus: 0px -1px 0px 0px rgb(255 255 255 / 0.16), 0px 0px 0px 1px rgb(255 255 255 / 0.12), 0px 0px 0px 1px rgb(159 18 57), 0px 0px 0px 2px rgb(24 24 27), 0px 0px 0px 4px rgb(96 165 250 / 0.8);
    --shadow-details-switch-background: 0px 1px 1px 0px rgb(0 0 0 / 0.1) inset, 0px 2px 4px 0px rgb(0 0 0 / 0.1) inset, 0px 0px 0px 0.75px rgb(255 255 255 / 0.12) inset, 0px 0px 8px 0px rgb(0 0 0 / 0.1) inset;
    --shadow-buttons-inverted-focus: 0px -1px 0px 0px rgb(255 255 255 / 0.12), 0px 0px 0px 1px rgb(255 255 255 / 0.12), 0px 0px 0px 1px rgb(82 82 91), 0px 0px 0px 2px rgb(24 24 27), 0px 0px 0px 4px rgb(96 165 250 / 0.8);
    --shadow-elevation-flyout: 0px -1px 0px 0px rgb(255 255 255 / 0.04), 0px 0px 0px 1px rgb(255 255 255 / 0.1), 0px 4px 8px 0px rgb(0 0 0 / 0.32), 0px 8px 16px 0px rgb(0 0 0 / 0.32);
    --shadow-borders-error: 0px 0px 0px 1px rgb(244 63 94), 0px 0px 0px 3px rgb(225 29 72 / 0.25);
    --shadow-buttons-inverted: 0px -1px 0px 0px rgb(255 255 255 / 0.12), 0px 0px 0px 1px rgb(255 255 255 / 0.1), 0px 0px 0px 1px rgb(82 82 91), 0px 0px 1px 1.5px rgb(0 0 0 / 0.24), 0px 2px 2px 0px rgb(0 0 0 / 0.24);
    --shadow-borders-base: 0px -1px 0px 0px rgb(255 255 255 / 0.06), 0px 0px 0px 1px rgb(255 255 255 / 0.06), 0px 0px 0px 1px rgb(39 39 42), 0px 0px 1px 1.5px rgb(0 0 0 / 0.24), 0px 2px 2px 0px rgb(0 0 0 / 0.24);
    --shadow-elevation-card-hover: 0px -1px 0px 0px rgb(255 255 255 / 0.06), 0px 0px 0px 1px rgb(255 255 255 / 0.06), 0px 0px 0px 1px rgb(39 39 42), 0px 1px 4px 0px rgb(0 0 0 / 0.48), 0px 2px 8px 0px rgb(0 0 0 / 0.48);
    --shadow-elevation-card-rest: 0px -1px 0px 0px rgb(255 255 255 / 0.06), 0px 0px 0px 1px rgb(255 255 255 / 0.06), 0px 0px 0px 1px rgb(39 39 42), 0px 1px 2px 0px rgb(0 0 0 / 0.32), 0px 2px 4px 0px rgb(0 0 0 / 0.32);
    --shadow-buttons-neutral: 0px -1px 0px 0px rgb(255 255 255 / 0.06), 0px 0px 0px 1px rgb(255 255 255 / 0.06), 0px 0px 0px 1px rgb(39 39 42), 0px 0px 1px 1.5px rgb(0 0 0 / 0.24), 0px 2px 2px 0px rgb(0 0 0 / 0.24);
    --shadow-elevation-code-block: 0px -1px 0px 0px rgb(255 255 255 / 0.06), 0px 0px 0px 1px rgb(255 255 255 / 0.06), 0px 0px 0px 1px rgb(39 39 42), 0px 1px 2px 0px rgb(0 0 0 / 0.32), 0px 2px 4px 0px rgb(0 0 0 / 0.32);
    --shadow-buttons-neutral-focus: 0px -1px 0px 0px rgb(255 255 255 / 0.06), 0px 0px 0px 1px rgb(255 255 255 / 0.06), 0px 0px 0px 1px rgb(39 39 42), 0px 0px 0px 2px rgb(24 24 27), 0px 0px 0px 4px rgb(96 165 250 / 0.8);
    --shadow-elevation-modal: 0px 0px 0px 1px rgb(24 24 27) inset, 0px 0px 0px 1.5px rgb(255 255 255 / 0.06) inset, 0px -1px 0px 0px rgb(255 255 255 / 0.04), 0px 0px 0px 1px rgb(255 255 255 / 0.1), 0px 4px 8px 0px rgb(0 0 0 / 0.32), 0px 8px 16px 0px rgb(0 0 0 / 0.32);
    --shadow-elevation-commandbar: 0px 0px 0px 0.75px rgb(24 24 27) inset, 0px 0px 0px 1.25px rgb(255 255 255 / 0.1) inset, 0px 4px 8px 0px rgb(0 0 0 / 0.32), 0px 8px 16px 0px rgb(0 0 0 / 0.32);
    --shadow-elevation-tooltip: 0px -1px 0px 0px rgb(255 255 255 / 0.04), 0px 2px 4px 0px rgb(0 0 0 / 0.32), 0px 0px 0px 1px rgb(255 255 255 / 0.1), 0px 4px 8px 0px rgb(0 0 0 / 0.32);
  }
}
