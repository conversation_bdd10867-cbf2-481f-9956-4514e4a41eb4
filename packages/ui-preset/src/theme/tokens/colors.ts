export const colors = {
  "dark": {
    "--button-danger-pressed": "rgba(225, 29, 72, 1)",
    "--bg-base-pressed": "rgba(63, 63, 70, 1)",
    "--bg-component-hover": "rgba(255, 255, 255, 0.1)",
    "--border-interactive": "rgba(96, 165, 250, 1)",
    "--button-neutral": "rgba(255, 255, 255, 0.04)",
    "--tag-orange-border": "rgba(124, 45, 18, 1)",
    "--tag-blue-text": "rgba(147, 197, 253, 1)",
    "--bg-highlight": "rgba(23, 37, 84, 1)",
    "--tag-neutral-icon": "rgba(113, 113, 122, 1)",
    "--bg-switch-off-hover": "rgba(82, 82, 91, 1)",
    "--fg-on-color": "rgba(255, 255, 255, 1)",
    "--button-inverted-pressed": "rgba(161, 161, 170, 1)",
    "--fg-interactive-hover": "rgba(147, 197, 253, 1)",
    "--fg-error": "rgba(251, 113, 133, 1)",
    "--bg-switch-off": "rgba(63, 63, 70, 1)",
    "--border-strong": "rgba(255, 255, 255, 0.16)",
    "--border-error": "rgba(251, 113, 133, 1)",
    "--fg-subtle": "rgba(161, 161, 170, 1)",
    "--bg-highlight-hover": "rgba(30, 58, 138, 1)",
    "--button-inverted": "rgba(82, 82, 91, 1)",
    "--tag-orange-text": "rgba(253, 186, 116, 1)",
    "--fg-base": "rgba(244, 244, 245, 1)",
    "--fg-disabled": "rgba(82, 82, 91, 1)",
    "--button-danger": "rgba(159, 18, 57, 1)",
    "--tag-neutral-border": "rgba(255, 255, 255, 0.06)",
    "--tag-blue-border": "rgba(30, 58, 138, 1)",
    "--tag-neutral-text": "rgba(212, 212, 216, 1)",
    "--tag-purple-border": "rgba(91, 33, 182, 1)",
    "--tag-green-text": "rgba(52, 211, 153, 1)",
    "--button-inverted-hover": "rgba(113, 113, 122, 1)",
    "--bg-component-pressed": "rgba(255, 255, 255, 0.16)",
    "--contrast-border-bot": "rgba(255, 255, 255, 0.08)",
    "--tag-blue-icon": "rgba(96, 165, 250, 1)",
    "--bg-field": "rgba(255, 255, 255, 0.04)",
    "--tag-neutral-bg": "rgba(255, 255, 255, 0.08)",
    "--tag-green-border": "rgba(6, 78, 59, 1)",
    "--tag-red-icon": "rgba(251, 113, 133, 1)",
    "--tag-red-text": "rgba(253, 164, 175, 1)",
    "--tag-purple-icon": "rgba(167, 139, 250, 1)",
    "--bg-interactive": "rgba(96, 165, 250, 1)",
    "--bg-field-hover": "rgba(255, 255, 255, 0.08)",
    "--border-transparent": "rgba(255, 255, 255, 0)",
    "--tag-orange-icon": "rgba(251, 146, 60, 1)",
    "--tag-purple-bg": "rgba(46, 16, 101, 1)",
    "--bg-base-hover": "rgba(39, 39, 42, 1)",
    "--tag-blue-bg": "rgba(23, 37, 84, 1)",
    "--tag-green-bg": "rgba(2, 44, 34, 1)",
    "--tag-purple-text": "rgba(196, 181, 253, 1)",
    "--tag-red-border": "rgba(136, 19, 55, 1)",
    "--border-danger": "rgba(190, 18, 60, 1)",
    "--tag-green-icon": "rgba(16, 185, 129, 1)",
    "--tag-red-bg": "rgba(76, 5, 25, 1)",
    "--fg-interactive": "rgba(96, 165, 250, 1)",
    "--tag-orange-bg": "rgba(67, 20, 7, 1)",
    "--button-danger-hover": "rgba(190, 18, 60, 1)",
    "--bg-component": "rgba(39, 39, 42, 1)",
    "--bg-disabled": "rgba(39, 39, 42, 1)",
    "--button-transparent": "rgba(255, 255, 255, 0)",
    "--border-menu-bot": "rgba(255, 255, 255, 0.08)",
    "--tag-purple-bg-hover": "rgba(91, 33, 182, 1)",
    "--tag-orange-bg-hover": "rgba(124, 45, 18, 1)",
    "--tag-blue-bg-hover": "rgba(30, 58, 138, 1)",
    "--tag-red-bg-hover": "rgba(136, 19, 55, 1)",
    "--tag-green-bg-hover": "rgba(6, 78, 59, 1)",
    "--border-menu-top": "rgba(33, 33, 36, 1)",
    "--bg-base": "rgba(33, 33, 36, 1)",
    "--contrast-border-top": "rgba(33, 33, 36, 1)",
    "--bg-field-component": "rgba(33, 33, 36, 1)",
    "--bg-subtle-hover": "rgba(33, 33, 36, 1)",
    "--bg-subtle": "rgba(24, 24, 27, 1)",
    "--fg-on-inverted": "rgba(24, 24, 27, 1)",
    "--bg-overlay": "rgba(24, 24, 27, 0.72)",
    "--button-transparent-hover": "rgba(255, 255, 255, 0.08)",
    "--contrast-fg-secondary": "rgba(255, 255, 255, 0.56)",
    "--contrast-border-base": "rgba(255, 255, 255, 0.16)",
    "--contrast-bg-base-pressed": "rgba(82, 82, 91, 1)",
    "--button-neutral-pressed": "rgba(255, 255, 255, 0.12)",
    "--border-base": "rgba(255, 255, 255, 0.08)",
    "--contrast-fg-primary": "rgba(255, 255, 255, 0.88)",
    "--button-neutral-hover": "rgba(255, 255, 255, 0.08)",
    "--contrast-bg-base": "rgba(39, 39, 42, 1)",
    "--tag-neutral-bg-hover": "rgba(255, 255, 255, 0.12)",
    "--contrast-bg-subtle": "rgba(255, 255, 255, 0.04)",
    "--contrast-bg-base-hover": "rgba(63, 63, 70, 1)",
    "--bg-field-component-hover": "rgba(39, 39, 42, 1)",
    "--bg-subtle-pressed": "rgba(39, 39, 42, 1)",
    "--button-transparent-pressed": "rgba(255, 255, 255, 0.12)",
    "--fg-muted": "rgba(113, 113, 122, 1)",
    "--alpha-400": "rgba(255, 255, 255, 0.24)",
    "--alpha-250": "rgba(255, 255, 255, 0.1)"
  },
  "light": {
    "--tag-neutral-border": "rgba(228, 228, 231, 1)",
    "--tag-neutral-icon": "rgba(161, 161, 170, 1)",
    "--bg-switch-off-hover": "rgba(212, 212, 216, 1)",
    "--border-menu-bot": "rgba(255, 255, 255, 1)",
    "--border-menu-top": "rgba(228, 228, 231, 1)",
    "--bg-subtle-hover": "rgba(244, 244, 245, 1)",
    "--contrast-fg-primary": "rgba(255, 255, 255, 0.88)",
    "--bg-switch-off": "rgba(228, 228, 231, 1)",
    "--contrast-bg-base-pressed": "rgba(63, 63, 70, 1)",
    "--bg-field-component-hover": "rgba(250, 250, 250, 1)",
    "--bg-base-pressed": "rgba(228, 228, 231, 1)",
    "--tag-neutral-text": "rgba(82, 82, 91, 1)",
    "--tag-red-text": "rgba(159, 18, 57, 1)",
    "--contrast-bg-base": "rgba(24, 24, 27, 1)",
    "--border-strong": "rgba(212, 212, 216, 1)",
    "--contrast-border-base": "rgba(255, 255, 255, 0.15)",
    "--bg-field": "rgba(250, 250, 250, 1)",
    "--tag-blue-text": "rgba(30, 64, 175, 1)",
    "--button-inverted-pressed": "rgba(82, 82, 91, 1)",
    "--border-interactive": "rgba(59, 130, 246, 1)",
    "--bg-base-hover": "rgba(244, 244, 245, 1)",
    "--contrast-bg-subtle": "rgba(39, 39, 42, 1)",
    "--bg-highlight": "rgba(239, 246, 255, 1)",
    "--contrast-fg-secondary": "rgba(255, 255, 255, 0.56)",
    "--tag-red-bg": "rgba(255, 228, 230, 1)",
    "--button-transparent": "rgba(255, 255, 255, 0)",
    "--button-danger-pressed": "rgba(159, 18, 57, 1)",
    "--fg-on-color": "rgba(255, 255, 255, 1)",
    "--button-inverted-hover": "rgba(63, 63, 70, 1)",
    "--bg-field-component": "rgba(255, 255, 255, 1)",
    "--tag-orange-text": "rgba(154, 52, 18, 1)",
    "--tag-green-icon": "rgba(16, 185, 129, 1)",
    "--border-base": "rgba(228, 228, 231, 1)",
    "--bg-base": "rgba(255, 255, 255, 1)",
    "--tag-orange-border": "rgba(254, 215, 170, 1)",
    "--tag-red-border": "rgba(254, 205, 211, 1)",
    "--tag-green-border": "rgba(167, 243, 208, 1)",
    "--tag-green-text": "rgba(6, 95, 70, 1)",
    "--button-neutral": "rgba(255, 255, 255, 1)",
    "--tag-blue-border": "rgba(191, 219, 254, 1)",
    "--fg-interactive-hover": "rgba(37, 99, 235, 1)",
    "--tag-orange-icon": "rgba(249, 115, 22, 1)",
    "--button-neutral-hover": "rgba(244, 244, 245, 1)",
    "--fg-interactive": "rgba(59, 130, 246, 1)",
    "--bg-component-pressed": "rgba(228, 228, 231, 1)",
    "--tag-purple-bg": "rgba(237, 233, 254, 1)",
    "--contrast-bg-base-hover": "rgba(39, 39, 42, 1)",
    "--bg-component": "rgba(250, 250, 250, 1)",
    "--bg-subtle": "rgba(250, 250, 250, 1)",
    "--tag-purple-text": "rgba(91, 33, 182, 1)",
    "--contrast-border-bot": "rgba(255, 255, 255, 0.1)",
    "--button-inverted": "rgba(39, 39, 42, 1)",
    "--tag-red-icon": "rgba(244, 63, 94, 1)",
    "--button-transparent-hover": "rgba(244, 244, 245, 1)",
    "--button-neutral-pressed": "rgba(228, 228, 231, 1)",
    "--tag-purple-icon": "rgba(167, 139, 250, 1)",
    "--bg-field-hover": "rgba(244, 244, 245, 1)",
    "--fg-on-inverted": "rgba(255, 255, 255, 1)",
    "--bg-interactive": "rgba(59, 130, 246, 1)",
    "--border-danger": "rgba(190, 18, 60, 1)",
    "--button-transparent-pressed": "rgba(228, 228, 231, 1)",
    "--tag-purple-border": "rgba(221, 214, 254, 1)",
    "--bg-highlight-hover": "rgba(219, 234, 254, 1)",
    "--border-error": "rgba(225, 29, 72, 1)",
    "--button-danger": "rgba(225, 29, 72, 1)",
    "--tag-blue-bg": "rgba(219, 234, 254, 1)",
    "--border-transparent": "rgba(255, 255, 255, 0)",
    "--button-danger-hover": "rgba(190, 18, 60, 1)",
    "--bg-subtle-pressed": "rgba(228, 228, 231, 1)",
    "--fg-error": "rgba(225, 29, 72, 1)",
    "--bg-component-hover": "rgba(244, 244, 245, 1)",
    "--bg-disabled": "rgba(244, 244, 245, 1)",
    "--tag-blue-icon": "rgba(96, 165, 250, 1)",
    "--fg-subtle": "rgba(82, 82, 91, 1)",
    "--tag-orange-bg-hover": "rgba(254, 215, 170, 1)",
    "--tag-green-bg-hover": "rgba(167, 243, 208, 1)",
    "--tag-red-bg-hover": "rgba(254, 205, 211, 1)",
    "--tag-purple-bg-hover": "rgba(221, 214, 254, 1)",
    "--tag-neutral-bg-hover": "rgba(228, 228, 231, 1)",
    "--tag-blue-bg-hover": "rgba(191, 219, 254, 1)",
    "--tag-green-bg": "rgba(209, 250, 229, 1)",
    "--tag-neutral-bg": "rgba(244, 244, 245, 1)",
    "--tag-orange-bg": "rgba(255, 237, 213, 1)",
    "--fg-base": "rgba(24, 24, 27, 1)",
    "--contrast-border-top": "rgba(24, 24, 27, 1)",
    "--bg-overlay": "rgba(24, 24, 27, 0.4)",
    "--fg-disabled": "rgba(161, 161, 170, 1)",
    "--fg-muted": "rgba(113, 113, 122, 1)",
    "--alpha-400": "rgba(24, 24, 27, 0.24)",
    "--alpha-250": "rgba(24, 24, 27, 0.1)"
  }
}