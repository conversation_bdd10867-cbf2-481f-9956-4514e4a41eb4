{"name": "@saf/ui-preset", "version": "1.0.0", "description": "SAF UI preset - A self-managed design system with CSS variables and Tailwind configuration", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/saf-frontend.git", "directory": "packages/ui-preset"}, "author": "SAF Team", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js"}, "./styles/base.css": "./dist/styles/base.css", "./styles/shadows.css": "./dist/styles/shadows.css", "./package.json": "./package.json"}, "types": "./dist/index.d.ts", "main": "./dist/index.js", "scripts": {"build": "tsup"}, "dependencies": {"@tailwindcss/forms": "^0.5.3", "tailwindcss-animate": "^1.0.6"}, "peerDependencies": {"tailwindcss": ">=3.0.0"}, "devDependencies": {"tailwindcss": "^3.4.1", "tsup": "^7.1.0", "typescript": "^5.1.6"}, "publishConfig": {"access": "public"}}